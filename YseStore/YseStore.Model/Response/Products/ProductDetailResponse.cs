using Entitys;

namespace YseStore.Model.Response.Products
{
    /// <summary>
    /// 用于前端Single页面展示的产品详情响应类
    /// </summary>
    public class ProductDetailResponse
    {
        /// <summary>
        /// 产品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 规格模式
        /// </summary>
        public int IsCombination { get; set; }

        /// <summary>
        /// 可选仓库列表
        /// </summary>
        public List<shipping_overseas> OptionalWarehouses { get; set; } = new List<shipping_overseas>();

        /// <summary>
        /// 产品变体列表
        /// </summary>
        public List<products_selected_attribute_combination> ProductVariants { get; set; } =
            new List<products_selected_attribute_combination>();

        /// <summary>
        /// 产品平均评分
        /// </summary>
        public decimal AvgRating { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 产品简短描述
        /// </summary>
        public string BriefDescription { get; set; } = string.Empty;

        /// <summary>
        /// 产品详细描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 产品SKU
        /// </summary>
        public string Sku { get; set; } = string.Empty;

        /// <summary>
        /// 产品价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 格式化后的产品价格
        /// </summary>
        public string PriceFormat { get; set; }

        /// <summary>
        /// 促销价
        /// </summary>
        public decimal PromotionPrice { get; set; }

        /// <summary>
        /// 格式化后的促销价
        /// </summary>
        public string PromotionPriceFormat { get; set; }

        /// <summary>
        /// 产品原价
        /// </summary>
        public decimal? OriginalPrice { get; set; }

        /// <summary>
        /// 格式化后的原价
        /// </summary>
        public string OriginalPriceFormat { get; set; }

        /// <summary>
        /// 产品主图路径
        /// </summary>
        public string PicPath { get; set; } = string.Empty;

        /// <summary>
        /// 产品图片列表
        /// </summary>
        public List<products_images> ProductImages { get; set; } = new List<products_images>();

        /// <summary>
        /// 产品视频URL
        /// </summary>
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 产品评分
        /// </summary>
        public decimal Rating { get; set; }

        /// <summary>
        /// 评论数量
        /// </summary>
        public int ReviewCount { get; set; }

        /// <summary>
        /// 是否有库存
        /// </summary>
        public bool IsInStock { get; set; }

        /// <summary>
        /// 品牌名称
        /// </summary>
        public string BrandName { get; set; } = string.Empty;

        /// <summary>
        /// 分类名称
        /// </summary>
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 动态产品属性选项（根据Name_en动态设置键名）
        /// 键为属性名称（Name_en），值为该属性的选项列表
        /// </summary>
        public Dictionary<string, List<AttributeOption>> DynamicAttributes { get; set; } =
            new Dictionary<string, List<AttributeOption>>();

        /// <summary>
        /// 产品标签
        /// </summary>
        public List<TagItem> Tags { get; set; } = new List<TagItem>();

        /// <summary>
        /// 产品特点列表
        /// </summary>
        public List<string> Features { get; set; } = new List<string>();

        /// <summary>
        /// 产品规格列表
        /// </summary>
        public List<SpecificationItem> Specifications { get; set; } = new List<SpecificationItem>();

        /// <summary>
        /// 产品评论列表
        /// </summary>
        public List<ReviewItem> Reviews { get; set; } = new List<ReviewItem>();

        /// <summary>
        /// 产品SEO信息
        /// </summary>
        public products_seo ProductSeo { get; set; }

        /// <summary>
        /// 是否已收藏
        /// </summary>
        public bool IsFavorited { get; set; }
    }
    

    /// <summary>
    /// 通用属性选项（用于动态属性）
    /// </summary>
    public class AttributeOption
    {
        /// <summary>
        /// 属性ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 选项名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 颜色代码（仅颜色属性使用）
        /// </summary>
        public string? ColorCode { get; set; }

        /// <summary>
        /// 选项图片路径（如果有）
        /// </summary>
        public string? ImagePath { get; set; }

        /// <summary>
        /// 额外数据（JSON格式，用于存储其他属性特定的数据）
        /// </summary>
        public string? ExtraData { get; set; }
    }

    /// <summary>
    /// 标签项
    /// </summary>
    public class TagItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
    }

    /// <summary>
    /// 规格项
    /// </summary>
    public class SpecificationItem
    {
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }

    /// <summary>
    /// 评论项
    /// </summary>
    public class ReviewItem
    {
        /// <summary>
        /// 评论ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 评论日期时间
        /// </summary>
        public DateTime ReviewDate { get; set; }

        /// <summary>
        /// 评论内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 评分（1-5）
        /// </summary>
        public int Rating { get; set; }

        /// <summary>
        /// 用户头像
        /// </summary>
        public string UserAvatar { get; set; } = string.Empty;

        /// <summary>
        /// 评论图片
        /// </summary>
        public List<string> Images { get; set; } = new List<string>();

        /// <summary>
        /// 评论回复
        /// </summary>
        public List<ReviewReplyItem> Replies { get; set; } = new List<ReviewReplyItem>();
    }

    /// <summary>
    /// 评论回复项
    /// </summary>
    public class ReviewReplyItem
    {
        /// <summary>
        /// 回复ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 回复人名称
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 回复日期时间
        /// </summary>
        public DateTime ReplyDate { get; set; }

        /// <summary>
        /// 回复内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 回复人身份
        /// </summary>
        public string Identity { get; set; } = string.Empty;

        /// <summary>
        /// 是否是管理员
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 被回复人的名称
        /// </summary>
        public string ReplyToName { get; set; } = string.Empty;

        /// <summary>
        /// 被回复的回复ID
        /// </summary>
        public int? ReplyToId { get; set; }
    }
}