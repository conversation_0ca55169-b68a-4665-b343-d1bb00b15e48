using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.RequestModels.Store
{
    public class SaveRequestModel
    {
        public int? EditPId { get; set; }
        public int? DraftsId { get; set; }
        public int? PagesId { get; set; }
        public string Page { get; set; }
        public long? AssociationId { get; set; }
        [BindProperty(Name = "PId[]")]
        public List<int> PId { get; set; } = new List<int>();

        [FromForm(Name = "visual")]
        public Dictionary<string, Dictionary<string, string>> VisualData { get; set; }

        //[FromForm(Name = "visual")]
        //public Dictionary<string, JObject> VisualData { get; set; }
    }

    public class CopyDraftRequest
    {
        public int DraftsId { get; set; }
        public bool IsRename { get; set; }
        public int ReturnType { get; set; } // 1 返回ID，0 返回成功消息
        public int IsTmp { get; set; }
    }

}
