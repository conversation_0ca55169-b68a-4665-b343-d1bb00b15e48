<!--Sidebar-->
<div class="col-12 col-sm-12 col-md-3 col-lg-3 sidebar">
    <div class="sidebar_widget">
        <div class="widget-title"><h2>{{ "blog.global.searchNote"|translate}}</h2></div>
        <div class="custom-search">
            <form class="input-group search-header search" role="search" style="position: relative;" id="blog-search-form">
                <input class="search-header__input search__input input-group__field" type="search" name="keyword"
                       value="{{ Model.Keyword }}"
                       placeholder="{{ "blog.global.blog"|translate}} {{ "blog.global.searchNote"|translate}}..."
                       aria-label="Search" autocomplete="off" id="blog-search-input">
                <span class="input-group__btn">
                    <button class="btnSearch" type="button" onclick="performBlogSearch()">
                        <i class="icon anb an-sistrix"></i>
                    </button>
                </span>
            </form>
        </div>
        <!-- 搜索结果提示 -->
        {% comment %}{% if Model.Keyword != "" %}{% endcomment %}
        {% comment %}<div class="search-result-info" style="margin-top: 10px; padding: 8px; background-color: #e3f2fd; border-radius: 4px; font-size: 12px;">{% endcomment %}
            {% comment %}<i class="icon an an-search" style="color: #1976d2;"></i>{% endcomment %}
            {% comment %}<span style="color: #1976d2;">搜索关键词: "<strong>{{ Model.Keyword }}</strong>"</span>{% endcomment %}
            {% comment %}<a href="/blog" style="color: #d32f2f; text-decoration: none; margin-left: 10px; font-size: 11px;">{% endcomment %}
                {% comment %}<i class="icon an an-times"></i> 清除搜索{% endcomment %}
            {% comment %}</a>{% endcomment %}
        {% comment %}</div>{% endcomment %}
        {% comment %}{% endif %}{% endcomment %}
    </div>
    <div class="sidebar_tags">
     {% if Model.settingsDynamic.Category == "1"  %}
        <div class="sidebar_widget categories">
            <div class="widget-title"><h2>{{ "blog.global.blog"|translate}} {{ "web.global.category"|translate}}</h2></div>
            <div class="widget-content">
                <ul class="sidebar_categories">
                    <!-- 全部分类链接 -->
                    {% comment %}<li class="lvl-1 {% if Model.CateId == "" %}active{% endif %}">{% endcomment %}
                        {% comment %}<a href="/blog{% if Model.Keyword != "" %}?keyword={{ Model.Keyword }}{% endif %}{% if Model.TagId != "" %}{% if Model.Keyword != "" %}&{% else %}?{% endif %}tagId={{ Model.TagId }}{% endif %}"{% endcomment %}
                           {% comment %}class="site-nav lvl-1">{{ "web.global.all"|translate}}</a>{% endcomment %}
                    {% comment %}</li>{% endcomment %}
                    <!-- 动态分类列表 -->
                    {% if Model.Categories and Model.Categories.size > 0 %}
                        {% for category in Model.Categories %}
                        <li class="lvl-1 {% if Model.CateId == category.CateId %}active{% endif %}">
                            <a href="/blog/category/{{ category.PageUrl }}{% if Model.Keyword != "" %}?keyword={{ Model.Keyword }}{% endif %}{% if Model.TagId != "" %}{% if Model.Keyword != "" %}&{% else %}?{% endif %}tagId={{ Model.TagId }}{% endif %}"
                               class="site-nav lvl-1">{{ category.Category }}</a>
                        </li>
                        {% endfor %}
                    {% else %}
                        <!-- 如果没有分类数据，显示默认分类 -->
                        {% comment %}<li class="lvl-1"><a href="#" class="site-nav lvl-1">暂无分类</a></li>{% endcomment %}
                    {% endif %}
                </ul>
            </div>
        </div>
        {% endif %}
         {% if Model.settingsDynamic.HotBlog == "1"  %}
        <div class="sidebar_widget">
            <div class="widget-title"><h2>{{ "blog.global.latestBlog"|translate}}   </h2></div>
            <div class="widget-content">
                <div class="list list-sidebar-products">
                    <div class="grid">
                        {% if Model.Blogs and Model.Blogs.size > 0 %}
                            {% assign maxBlogs = 4 %}
                            {% for blog in Model.Blogs limit: maxBlogs %}
                        <div class="grid__item">
                            <div class="mini-list-item">
                                <div class="mini-view_image">
                                    <a class="grid-view-item__link" href="/blog/{{ blog.PageUrl }}">
                                        {% if blog.PicPath and blog.PicPath != "" %}
                                        <img class="grid-view-item__image blur-up lazyload sidebar-blog-image" data-src="{{ blog.PicPath }}" src="{{ blog.PicPath }}" alt="{{ blog.Title }}">
                                        {% else %}
                                        <img class="grid-view-item__image blur-up lazyload sidebar-blog-image" data-src="{{static_path}}/assets/images/blog/post-thumb{{ forloop.index }}-90x880.jpg" src="{{static_path}}/assets/images/blog/post-thumb{{ forloop.index }}-90x880.jpg" alt="{{ blog.Title }}">
                                        {% endif %}
                                    </a>
                                </div>
                                <div class="details">
                                    <a class="grid-view-item__title" href="/blog/{{ blog.PageUrl }}">{{ blog.Title }}</a>
                                    <div class="grid-view-item__meta"><span class="article__date">{% if blog.StandardAccTime %}{{ blog.StandardAccTime }}{% else %}{{ blog.AccTime | date: "%b %d, %Y" }}{% endif %}</span></div>
                                </div>
                            </div>
                        </div>
                            {% endfor %}
      
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
         {% endif %}
        
        <div class="sidebar_widget tags-clouds">
            <div class="widget-title"><h2>{{ "blog.global.tags"|translate}}</h2></div>
            <div class="widget-content tags-scrollable">
                <ul>
                    {% if Model.Tags and Model.Tags.size > 0 %}
                        {% for tag in Model.Tags %}
                        <li>
                            <a href="/blog?tagId={{ tag.TId }}{% if Model.Keyword != "" %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId != "" %}&cateId={{ Model.CateId }}{% endif %}"
                               class="{% if Model.TagId == tag.TId %}active{% endif %}">
                                {{ tag.Name }}
                                {% if Model.TagId == tag.TId %}
                                <i class="icon an an-check" style="margin-left: 5px; font-size: 10px;"></i>
                                {% endif %}
                            </a>
                        </li>
                        {% endfor %}
                    {% else %}
                        <!-- 如果没有标签数据，显示默认标签 -->
                        <li><a href="#">{{ "web.global.no_data"|translate}}</a></li>
                    {% endif %}
                </ul>
            </div>
        </div>
        
    </div>
</div>

<!-- 博客搜索功能脚本 -->
<script>
// 执行博客搜索的全局函数
function performBlogSearch() {
    const searchInput = document.getElementById('blog-search-input');
    if (!searchInput) return;

    const keyword = searchInput.value.trim();
    const currentCateId = '{{ Model.CateId }}';
    const currentTagId = '{{ Model.TagId }}';

    // 构建搜索URL
    let searchUrl = '/blog';
    const params = [];

    if (keyword) {
        params.push('keyword=' + encodeURIComponent(keyword));
    }
    if (currentCateId) {
        params.push('cateId=' + encodeURIComponent(currentCateId));
    }
    if (currentTagId) {
        params.push('tagId=' + encodeURIComponent(currentTagId));
    }

    if (params.length > 0) {
        searchUrl += '?' + params.join('&');
    }

    // 使用普通页面跳转避免HTMX问题
    window.location.href = searchUrl;
}

// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('blog-search-input');

    if (searchInput) {
        // 回车键搜索
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performBlogSearch();
            }
        });
    }

    // 清除搜索功能
    const clearSearchLinks = document.querySelectorAll('a[href="/blog"]');
    clearSearchLinks.forEach(function(link) {
        if (link.textContent.includes('清除搜索')) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                // 使用普通页面跳转避免HTMX问题
                window.location.href = '/blog';
            });
        }
    });
});
</script>

<!--End Sidebar-->