{% comment %} 动态推荐产品滑块 - t600主题专用 {% endcomment %}
{% if RecommendProducts and RecommendProducts.size > 0 %}
    {% for recommendGroup in RecommendProducts %}
        {% if recommendGroup.IsVisible and recommendGroup.Products.size > 0 %}
            <div class="row g-4 item-2" data-recommend-id="{{ recommendGroup.RId }}">
                {% for product in recommendGroup.Products %}
                <div class="col-md-6 col-lg-3">
                    <div class="product-item">
                        <div class="product-img">
                            {% if product.TypeLabel != null and product.TypeLabel != "" %}
                            <span class="type {% if product.TypeLabel == "Hot" %}hot{% elsif product.TypeLabel=="Out Of Stock" %}oos{% endif %}">{{ product.TypeLabel }}</span>
                            {% endif %}
                            <a href="/products/{{ product.PageUrl }}"
                               hx-get="/products/{{ product.PageUrl }}" hx-target="#main"
                               hx-push-url="true" hx-swap="innerHTML">
                                <img src="{% if product.PicPath != null and product.PicPath != '' %}{{ product.PicPath }}{% else %}/assets/img/product/01.png{% endif %}"
                                     alt="{{ product.ProductName }}" />
                            </a>
                            <div class="product-action-wrap">
                                <div class="product-action">
                                    <a href="#" data-bs-toggle="modal" data-bs-target="#quickview"
                                       data-bs-placement="right" data-tooltip="tooltip"
                                       title="{{ "web.global.quickView" | translate }}"
                                       data-product-id="{{ product.ProductId }}">
                                        <i class="far fa-eye"></i>
                                    </a>
                                    <a href="#" data-bs-placement="right" data-tooltip="tooltip"
                                       title="{{ "products.goods.addToFavorites" | translate }}"
                                       class="add-to-wishlist" data-product-id="{{ product.ProductId }}">
                                        <i class="far fa-heart{% if product.IsFavorited %} text-danger{% endif %}"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="product-content">
                            <h3 class="product-title">
                                <a href="/products/{{ product.PageUrl }}"
                                   hx-get="/products/{{ product.PageUrl }}" hx-target="#main"
                                   hx-push-url="true" hx-swap="innerHTML">{{ product.ProductName }}</a>
                            </h3>
                            <div class="product-rate">
                                {% assign fullStars = product.Rating | floor %}
                                {% assign halfStar = product.Rating | minus: fullStars %}
                                {% assign nextStar = fullStars | plus: 1 %}
                                {% for i in (1..5) %}
                                {% if i <= fullStars %}
                                <i class="fas fa-star"></i>
                                {% elsif halfStar >= 0.5 and i == nextStar %}
                                <i class="fas fa-star-half-alt"></i>
                                {% else %}
                                <i class="far fa-star"></i>
                                {% endif %}
                                {% endfor %}
                            </div>
                            <div class="product-bottom">
                                <div class="product-price">
                                    {% if product.PromotionPriceFormat and product.PromotionPriceFormat != "" and product.PromotionPriceFormat != "0" %}
                                    <del>{{ product.PriceFormat }}</del>
                                    <span>{{ product.PromotionPriceFormat }}</span>
                                    {% elsif product.OriginalPrice != null and product.OriginalPrice > product.Price %}
                                    <del>{{ product.OriginalPriceFormat }}</del>
                                    <span>{{ product.PriceFormat }}</span>
                                    {% else %}
                                    <span>{{ product.PriceFormat }}</span>
                                    {% endif %}
                                </div>
                                <button type="button" class="product-cart-btn" data-bs-placement="left"
                                        data-tooltip="tooltip"
                                        title="{{ "products.goods.addToCart" | translate }}"
                                        {% if product.IsInStock==false %}disabled{% endif %}
                                        data-product-id="{{ product.ProductId }}">
                                    <i class="far fa-shopping-bag"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endfor %}
{% else %}
    <!-- 如果没有推荐产品数据，显示默认内容或隐藏 -->
{% endif %}
