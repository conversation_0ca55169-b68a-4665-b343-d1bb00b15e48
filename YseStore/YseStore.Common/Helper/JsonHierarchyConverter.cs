using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common.Helper
{
    public class JsonHierarchyConverter
    {
        public static JObject ConvertToHierarchicalJson(List<KeyValuePair> input)
        {
            var root = new JObject();

            foreach (var item in input)
            {
                var name = item.Key;
                var value = item.Value.FirstOrDefault();

                // 分割name字段
                var parts = SplitNameIntoParts(name);

                // 构建层级结构
                JObject current = root;

                for (int i = 0; i < parts.Count - 1; i++)
                {
                    var part = parts[i];

                    if (!current.ContainsKey(part))
                    {
                        current[part] = new JObject();
                    }

                    current = (JObject)current[part];
                }

                // 设置最终值
                var lastPart = parts[parts.Count - 1];
                if (lastPart.IsNullOrEmpty())
                {

                }
                else
                {
                    current[lastPart] = value;
                }
            }

            return root;
        }

        private static List<string> SplitNameIntoParts(string name)
        {
            var parts = new List<string>();
            int start = 0;
            int end = 0;

            // 处理前缀（中括号前的部分）
            end = name.IndexOf('[');
            if (end == -1)
            {
                parts.Add(name);
                return parts;
            }

            if (end > 0)
            {
                parts.Add(name.Substring(0, end));
            }

            // 处理中括号内的内容
            while (end != -1)
            {
                start = end + 1;
                end = name.IndexOf(']', start);
                if (end == -1) break;

                var part = name.Substring(start, end - start);
                parts.Add(part);

                start = end + 1;
                end = name.IndexOf('[', start);
            }
            parts.Remove("");
            return parts;
        }
    }

    public class KeyValuePair
    {
        public string Key { get; set; }
        public List<string> Value { get; set; }
    }
}
