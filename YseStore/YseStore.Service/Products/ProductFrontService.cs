using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SqlSugar;
using YseStore.IService.Products;
using YseStore.IService.Sales;
using YseStore.Model;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.Response;
using YseStore.Model.Response.Products;
using YseStore.Repo;

namespace YseStore.Service.Products
{
    /// <summary>
    /// 产品前端服务实现类
    /// </summary>
    public class ProductFrontService : BaseServices<products>, IProductFrontService
    {
        private readonly ICurrencyService _currencyService;
        private readonly IFlashSaleService _flashSaleService;
        private readonly ILogger<products> _logger;

        public ProductFrontService(IBaseRepository<products> baseDal, ICurrencyService currencyService,
            ILogger<products> logger,
            IFlashSaleService flashSaleService) : base(baseDal)
        {
            _currencyService = currencyService;
            _logger = logger;
            _flashSaleService = flashSaleService;
        }

        /// <summary>
        /// 获取前端展示的产品列表，支持搜索和分页
        /// </summary>
        /// <param name="queryRequest">前端查询请求参数</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <param name="userId">当前用户ID（用于查询收藏状态，0表示未登录）</param>
        /// <returns>分页后的前端产品列表</returns>
        public async Task<PageModel<ProductFrontendResponse>> GetFrontendProductList(
            ProductFrontendQueryRequest queryRequest, string currentCurrency = "", int userId = 0)
        {
            try
            {
                // 创建产品查询对象，暂不进行Select映射
                var query = Db.Queryable<products>()
                    .LeftJoin<products_images>((p, img) => p.ProId == img.ProId && img.Position == 1);

                // 搜索条件：产品名称、描述包含关键词
                if (!string.IsNullOrEmpty(queryRequest.Keyword))
                {
                    query = query.Where(p => p.Name_en.Contains(queryRequest.Keyword) ||
                                             p.BriefDescription_en.Contains(queryRequest.Keyword));
                }

                // 按分类ID筛选
                string categoryOrderType = null;
                if (!string.IsNullOrEmpty(queryRequest.CategoryId))
                {
                    try
                    {
                        // 获取指定分类下的所有产品ID
                        var productIdsInCategory = await Db.Queryable<products_category_relate>()
                            .Where(x => x.CateId == Convert.ToInt32(queryRequest.CategoryId))
                            .Select(x => x.ProId)
                            .Distinct()
                            .ToListAsync();

                        // 使用这些产品ID进行筛选
                        query = query.Where(p => productIdsInCategory.Contains(p.ProId));

                        // 获取分类的排序方式
                        var category = await Db.Queryable<products_category>()
                            .Where(c => c.CateId == Convert.ToInt32(queryRequest.CategoryId))
                            .FirstAsync();

                        if (category != null && !string.IsNullOrEmpty(category.OrderType))
                        {
                            categoryOrderType = category.OrderType;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "查询分类产品或排序方式时出错，CategoryId: {CategoryId}", queryRequest.CategoryId);
                        // 出错时继续执行，不影响主流程
                    }
                }

                // 按价格范围筛选
                if (queryRequest.MinPrice.HasValue)
                {
                    query = query.Where(p => p.Price_1 >= queryRequest.MinPrice.Value);
                }

                if (queryRequest.MaxPrice.HasValue)
                {
                    query = query.Where(p => p.Price_1 <= queryRequest.MaxPrice.Value);
                }

                // 如果不包含已售罄商品，则过滤掉
                // if (!queryRequest.IncludeSoldOut)
                // {
                //     query = query.Where(p => !p.SoldOut);
                // }

                // 设置排序 - 优先使用用户指定的排序，其次使用分类的排序方式
                string effectiveSortBy = queryRequest.SortBy?.ToLower();

                // 如果用户没有指定排序方式，且分类有排序设置，则使用分类的排序方式
                if (string.IsNullOrEmpty(effectiveSortBy) && !string.IsNullOrEmpty(categoryOrderType))
                {
                    effectiveSortBy = categoryOrderType.ToLower();
                }

                query = effectiveSortBy switch
                {
                    // 按产品名称升序排列
                    "title-ascending" => query.OrderBy(p => p.Name_en),

                    // 按销量排序（这里使用评分作为替代，因为没有销量字段）
                    "bestseller" => query.OrderByDescending(p => p.Rating),

                    // 按字母顺序升序（A-Z）
                    "alpha-asc" => query.OrderBy(p => p.Name_en),

                    // 按字母顺序降序（Z-A）
                    "alpha-desc" => query.OrderByDescending(p => p.Name_en),

                    // 按价格升序排列 - 支持分类OrderType的price_asc格式
                    "priceasc" or "price_asc" => query.OrderBy(p => p.Price_1),

                    // 按价格降序排列 - 支持分类OrderType的price_desc格式
                    "pricedesc" or "price_desc" => query.OrderByDescending(p => p.Price_1),

                    // 按日期排序，最新的在前 - 支持分类OrderType的time_desc格式
                    "date-newest" or "time_desc" => query.OrderByDescending(p => p.AccTime),

                    // 按日期排序，最旧的在前 - 支持分类OrderType的time_asc格式
                    "date-oldest" or "time_asc" => query.OrderBy(p => p.AccTime),

                    // 保留原有的latest排序（向后兼容）
                    "latest" => query.OrderByDescending(p => p.AccTime),

                    // 手动排序 - 根据products_category_relate表中的记录顺序排序
                    "custom_sort" => ApplyCustomSort(),

                    // 默认排序：先展示推荐商品，再按ID降序（新品在前）
                    _ => query.OrderByDescending(p => p.ProId)
                };

                // 局部函数：处理自定义排序
                ISugarQueryable<products, products_images> ApplyCustomSort()
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(queryRequest.CategoryId))
                        {
                            // 根据products_category_relate表中的id字段排序（id越小排序越靠前）
                            return query.OrderBy(p => SqlFunc.Subqueryable<products_category_relate>()
                                    .Where(pcr =>
                                        pcr.ProId == p.ProId && pcr.CateId == Convert.ToInt32(queryRequest.CategoryId))
                                    .Select(pcr => pcr.id))
                                .OrderBy(p => p.ProId); // 作为备用排序
                        }
                        else
                        {
                            // 没有分类ID时，按ProId降序
                            return query.OrderByDescending(p => p.ProId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "查询手动排序数据时出错，CategoryId: {CategoryId}", queryRequest.CategoryId);
                        // 出错时使用默认排序
                        return query.OrderByDescending(p => p.ProId);
                    }
                }

                //过滤掉已下架商品
                query = query.Where(p => p.SoldOut == false);

                // 获取总记录数（已过滤已下架商品）
                var totalCount = await query.CountAsync();

                var productList = await query
                    .Select(p => new ProductFrontendResponse
                    {
                        ProductId = p.ProId,
                        PageUrl = p.PageUrl,
                        ProductName = p.Name_en,
                        PicPath = p.PicPath_0,
                        PicPath_1 = p.PicPath_1,
                        Price = p.Price_1,
                        OriginalPrice = p.Price_0,
                        Rating = p.Rating,
                        BriefDescription = p.BriefDescription_en,
                        Stock = SqlFunc.Subqueryable<products_selected_attribute_combination>()
                            .Where(c => c.ProId == p.ProId)
                            .Sum(c => c.Stock) ?? 0,
                        IsInStock = SqlFunc.Subqueryable<products_selected_attribute_combination>()
                            .Where(c => c.ProId == p.ProId)
                            .Sum(c => c.Stock) > 0,
                        AccTime = p.AccTime
                    })
                    .ToPageListAsync(queryRequest.PageIndex, queryRequest.PageSize);


                // 如果用户已登录，查询收藏状态
                if (userId > 0 && productList.Any())
                {
                    var productIds = productList.Select(p => p.ProductId).ToList();
                    var favoriteProductIds = await Db.Queryable<user_favorite>()
                        .Where(f => f.UserId == userId && productIds.Contains(f.ProId ?? 0))
                        .Select(f => f.ProId)
                        .ToListAsync();

                    // 更新产品的收藏状态
                    foreach (var product in productList)
                    {
                        product.IsFavorited = favoriteProductIds.Contains(product.ProductId);
                    }
                }

                // 获取币种信息
                var userCurrency = await _currencyService.GetCurrency(currentCurrency);
                var manageCurrency = await _currencyService.GetManageDefaultCurrency();

                // 处理产品类型标签、折扣和币种格式化
                foreach (var product in productList)
                {
                    decimal? Price = product.Price;
                    decimal? OriginalPrice = product.OriginalPrice;
                    // 获取促销价格信息
                    decimal promotionPrice = 0;
                    string promotionPriceFormat = null;
                    try
                    {
                        // 获取产品完整信息用于促销计算
                        var fullProduct = await Db.Queryable<products>().FirstAsync(p => p.ProId == product.ProductId);
                        if (fullProduct.IsCombination == 2)
                        {
                            var combinations = await Db.Queryable<products_selected_attribute_combination>()
                                .Where(c => c.ProId == product.ProductId && c.VariantsId != "")
                                .FirstAsync();
                            Price += combinations.Price;
                            OriginalPrice += combinations.OldPrice;
                        }

                        if (fullProduct != null)
                        {
                            var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                                fullProduct,
                                Price ?? 0,
                                OriginalPrice ?? 0,
                                "", // variantsId
                                0 // userId
                            );

                            if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                            {
                                promotionPrice = flashSaleResult.Price;
                                var promotionPriceResult =
                                    _currencyService.ShowPriceFormat(promotionPrice, userCurrency, manageCurrency);
                                promotionPriceFormat = promotionPriceResult.Item2;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"获取产品{product.ProductId}促销价格失败: {ex.Message}");
                        // 出错时促销价格保持为0
                    }

                    // 正确的价格显示逻辑：原价始终不变，促销价作用在售价上
                    if (promotionPrice > 0)
                    {
                        // 有促销价时：现价位置显示促销价，促销价字段显示促销价，原价保持不变
                        product.PriceFormat = promotionPriceFormat; // 现价位置显示促销价
                        product.PromotionPrice = promotionPrice;
                        product.PromotionPriceFormat = promotionPriceFormat;

                        // 原价位置显示真正的原价（Price_0对应的OriginalPrice）
                        if (OriginalPrice > 0)
                        {
                            var originalPriceResult =
                                _currencyService.ShowPriceFormat(OriginalPrice.Value, userCurrency,
                                    manageCurrency);
                            product.OriginalPriceFormat = originalPriceResult.Item2;
                        }
                    }
                    else
                    {
                        // 没有促销价时：正常显示现价和原价
                        // 格式化销售价格
                        if (Price > 0)
                        {
                            var priceResult =
                                _currencyService.ShowPriceFormat(Price.Value, userCurrency, manageCurrency);
                            product.PriceFormat = priceResult.Item2;
                        }

                        // 如果原价大于售价，显示原价作为划线价
                        if (OriginalPrice.HasValue && OriginalPrice > 0 &&
                            Price.HasValue && OriginalPrice > Price)
                        {
                            var originalPriceResult =
                                _currencyService.ShowPriceFormat(OriginalPrice.Value, userCurrency,
                                    manageCurrency);
                            product.OriginalPriceFormat = originalPriceResult.Item2;
                        }
                    }

                    // 获取评论统计信息
                    try
                    {
                        var reviewStats = await GetProductReviewStats(product.ProductId);
                        product.AvgRating = reviewStats.avgRating;
                        product.ReviewCount = reviewStats.reviewCount;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"获取产品{product.ProductId}评论统计失败: {ex.Message}");
                        product.AvgRating = 0;
                        product.ReviewCount = 0;
                    }

                    // 设置产品类型标签
                    if (!product.IsInStock.HasValue)
                    {
                        product.TypeLabel = "Out Of Stock";
                    }
                    else if (product.IsNew)
                    {
                        product.TypeLabel = "New";
                    }
                    else if (product.IsHot)
                    {
                        product.TypeLabel = "Hot";
                    }
                }

                // 构建并返回分页模型
                return new PageModel<ProductFrontendResponse>
                {
                    page = queryRequest.PageIndex,
                    PageSize = queryRequest.PageSize,
                    dataCount = totalCount,
                    data = productList
                };
            }
            catch (Exception ex)
            {
                // 记录异常
                Console.WriteLine($"获取前端产品列表时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 出错时返回空结果
                return new PageModel<ProductFrontendResponse>
                {
                    page = queryRequest.PageIndex,
                    PageSize = queryRequest.PageSize,
                    dataCount = 0,
                    data = new List<ProductFrontendResponse>()
                };
            }
        }

        /// <summary>
        /// 获取产品快速预览数据
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <param name="userId">用户ID，用于查询收藏状态</param>
        /// <returns>产品快速预览数据</returns>
        public async Task<ProductQuickViewResponse> GetProductQuickView(int productId, string currentCurrency = "",
            int userId = 0)
        {
            try
            {
                // 获取产品基本信息
                var product = await Db.Queryable<products>()
                    .Where(p => p.ProId == productId)
                    .FirstAsync();

                if (product == null)
                {
                    return null;
                }

                // 获取产品主图
                var productImage = await Db.Queryable<products_images>()
                    .Where(img => img.ProId == productId && img.Position == 1)
                    .FirstAsync();

                // 获取产品分类
                var categoryRelates = await Db.Queryable<products_category_relate>()
                    .Where(pcr => pcr.ProId == productId)
                    .ToListAsync();

                string categoryName = "Uncategorized";
                if (categoryRelates != null && categoryRelates.Count > 0)
                {
                    var categoryIds = categoryRelates.Select(cr => cr.CateId).ToList();
                    var categories = await Db.Queryable<products_category>()
                        .Where(pc => categoryIds.Contains(pc.CateId))
                        .ToListAsync();

                    if (categories != null && categories.Count > 0)
                    {
                        categoryName = string.Join(", ", categories.Select(c => c.Category_en));
                    }
                }

                var imagesList = await Db.Queryable<products_images>().Where(i => i.ProId == product.ProId)
                    .ToListAsync();

                // 获取币种信息
                var userCurrency = await _currencyService.GetCurrency(currentCurrency);
                var manageCurrency = await _currencyService.GetManageDefaultCurrency();

                // 获取促销价格信息
                decimal promotionPrice = 0;
                string promotionPriceFormat = null;
                try
                {
                    var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                        product,
                        product.Price_1 ?? 0,
                        product.Price_0 ?? 0,
                        "", // variantsId
                        0 // userId
                    );

                    if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                    {
                        promotionPrice = flashSaleResult.Price;
                        var promotionPriceResult =
                            _currencyService.ShowPriceFormat(promotionPrice, userCurrency, manageCurrency);
                        promotionPriceFormat = promotionPriceResult.Item2;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取产品{productId}促销价格失败: {ex.Message}");
                }

                // 正确的价格显示逻辑：原价始终不变，促销价作用在售价上
                string priceFormat = null;
                string originalPriceFormat = null;

                if (promotionPrice > 0)
                {
                    // 有促销价时：现价位置显示促销价，原价保持不变
                    priceFormat = promotionPriceFormat; // 现价位置显示促销价

                    // 原价位置显示真正的原价（Price_0）
                    if (product.Price_0.HasValue && product.Price_0.Value > 0)
                    {
                        var originalPriceResult =
                            _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                        originalPriceFormat = originalPriceResult.Item2;
                    }
                }
                else
                {
                    // 没有促销价时：正常显示现价和原价
                    // 格式化销售价格
                    if (product.Price_1.HasValue)
                    {
                        var priceResult =
                            _currencyService.ShowPriceFormat(product.Price_1.Value, userCurrency, manageCurrency);
                        priceFormat = priceResult.Item2;
                    }

                    // 如果原价大于售价，显示原价作为划线价
                    if (product.Price_0.HasValue && product.Price_0.Value > 0 &&
                        product.Price_1.HasValue && product.Price_0.Value > product.Price_1.Value)
                    {
                        var originalPriceResult =
                            _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                        originalPriceFormat = originalPriceResult.Item2;
                    }
                }

                // 获取产品变体
                var combinations = await Db.Queryable<products_selected_attribute_combination>()
                    .Where(c => c.ProId == productId).Select(t => new products_selected_attribute_combination
                    {
                        CId = t.CId,
                        ProId = t.ProId,
                        VariantsId = t.VariantsId,
                        OvId = t.OvId,
                        Title = t.Title,
                        SKU = t.SKU,
                        OldPrice = t.OldPrice,
                        PromotionPriceFormat = "0",
                        Price = t.Price,
                        CostPrice = t.CostPrice,
                        Stock = t.Stock,
                        Weight = t.Weight,
                        ImageId = t.ImageId
                    })
                    .ToListAsync();

                // 为变体设置图片路径
                if (combinations.Any())
                {
                    // 获取所有变体的图片ID
                    var imageIds = combinations
                        .Where(c => !string.IsNullOrEmpty(c.ImageId) && int.TryParse(c.ImageId, out _))
                        .Select(c => int.Parse(c.ImageId))
                        .Distinct()
                        .ToList();

                    // 批量查询图片信息
                    Dictionary<int, string> imagePaths = new Dictionary<int, string>();
                    if (imageIds.Any())
                    {
                        var images = await Db.Queryable<products_images>()
                            .Where(img => imageIds.Contains(img.ImageId) && img.ProId == productId)
                            .ToListAsync();

                        imagePaths = images.ToDictionary(img => img.ImageId, img => img.PicPath);
                    }

                    // 为每个变体设置图片路径
                    foreach (var combination in combinations)
                    {
                        if (!string.IsNullOrEmpty(combination.ImageId) &&
                            int.TryParse(combination.ImageId, out int imageId) &&
                            imagePaths.TryGetValue(imageId, out string picPath))
                        {
                            combination.PicPath = picPath;
                        }
                    }
                }

                // 处理每个变体的价格信息
                foreach (var variant in combinations)
                {
                    // 为每个变体计算促销价格
                    decimal variantPromotionPrice = 0;
                    try
                    {
                        var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                            product,
                            variant.Price ?? 0,
                            variant.OldPrice,
                            variant.VariantsId, // 使用变体ID
                            0 // userId
                        );

                        if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                        {
                            variantPromotionPrice = flashSaleResult.Price;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"获取产品{productId}变体{variant.VariantsId}促销价格失败: {ex.Message}");
                    }

                    // 设置促销价格数值
                    variant.PromotionPrice = variantPromotionPrice;

                    // 正确的变体价格显示逻辑：原价始终不变，促销价作用在售价上
                    if (variantPromotionPrice > 0)
                    {
                        // 有促销价时：现价位置显示促销价，原价保持不变
                        var promotionPriceResult =
                            _currencyService.ShowPriceFormat(variantPromotionPrice, userCurrency, manageCurrency);
                        variant.PriceFormat = promotionPriceResult.Item2; // 现价位置显示促销价
                        variant.PromotionPriceFormat = promotionPriceResult.Item2;

                        // 原价位置显示真正的原价（OldPrice）
                        if (variant.OldPrice > 0)
                        {
                            var originalPriceResult =
                                _currencyService.ShowPriceFormat(variant.OldPrice, userCurrency, manageCurrency);
                            variant.OldPriceFormat = originalPriceResult.Item2;
                        }
                    }
                    else
                    {
                        // 没有促销价时：正常显示现价和原价
                        // 格式化销售价格并赋值给PriceFormat字段
                        if (variant.Price.HasValue)
                        {
                            var priceResult =
                                _currencyService.ShowPriceFormat(variant.Price.Value, userCurrency, manageCurrency);
                            variant.PriceFormat = priceResult.Item2;
                        }

                        // 格式化原价并赋值给OldPriceFormat字段（只有当原价大于销售价时才显示）
                        if (variant.OldPrice > 0 && variant.OldPrice > variant.Price)
                        {
                            var oldPriceResult =
                                _currencyService.ShowPriceFormat(variant.OldPrice, userCurrency, manageCurrency);
                            variant.OldPriceFormat = oldPriceResult.Item2;
                        }

                        // 没有促销价时PromotionPriceFormat保持为"0"
                        variant.PromotionPriceFormat = "0";
                    }
                }

                // 根据产品组合模式决定仓库查询逻辑
                List<shipping_overseas> listOv;
                if (product.IsCombination == 2)
                {
                    // 当IsCombination=2时，根据变体的Title去仓库表中查找匹配名字的仓库
                    var variantTitles = combinations.Where(c => !string.IsNullOrEmpty(c.Title))
                        .Select(c => c.Title.Trim())
                        .Distinct()
                        .ToList();

                    if (variantTitles.Any())
                    {
                        listOv = await Db.Queryable<shipping_overseas>()
                            .Where(w => variantTitles.Contains(w.Name.Trim()))
                            .OrderBy(w => w.MyOrder)
                            .ToListAsync();
                    }
                    else
                    {
                        // 如果没有有效的Title，返回空列表
                        listOv = new List<shipping_overseas>();
                    }
                }
                else
                {
                    // 按照原来的逻辑（根据OvId查询）
                    listOv = await Db.Queryable<shipping_overseas>()
                        .Where(w => combinations.Select(c => c.OvId).Contains(w.OvId))
                        .OrderBy(w => w.MyOrder)
                        .ToListAsync();
                }

                // 获取产品属性和动态属性选项
                var dynamicAttributes = new Dictionary<string, List<AttributeOption>>();
                var productAttributes = await Db.Queryable<products_attribute>()
                    .Where(pa => pa.ProId == productId)
                    .OrderBy(pa => pa.Position)
                    .ToListAsync();

                // 获取图片列表用于处理属性图片
                var picturesList = await Db.Queryable<products_images>()
                    .Where(pi => pi.ProId == productId)
                    .ToListAsync();

                // 处理每个属性
                foreach (var attr in productAttributes)
                {
                    string attributeName = attr.Name_en;
                    // 确保字典中有该属性的键
                    if (!dynamicAttributes.ContainsKey(attributeName))
                    {
                        dynamicAttributes[attributeName] = new List<AttributeOption>();
                    }

                    // 统一处理所有属性类型
                    try
                    {
                        // 优先处理OptionsData字段（通常用于颜色等复杂属性）
                        if (!string.IsNullOrEmpty(attr.OptionsData))
                        {
                            // 处理复杂属性数据（如颜色）
                            var optionsData = attr.OptionsData.Trim();
                            var colorDict =
                                JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, object>>>(
                                    optionsData);
                            if (colorDict != null)
                            {
                                foreach (var item in colorDict)
                                {
                                    string optionName = item.Key;
                                    string colorCode = null;
                                    string imagePath = null;

                                    // 获取颜色代码
                                    if (item.Value.ContainsKey("color") && item.Value["color"] != null)
                                    {
                                        colorCode = item.Value["color"].ToString();
                                    }

                                    // 获取图片路径（如果有）
                                    if (item.Value.ContainsKey("picture") && item.Value["picture"] != null)
                                    {
                                        var pictureArray = item.Value["picture"];
                                        if (pictureArray != null && pictureArray.ToString() != "[]")
                                        {
                                            imagePath = pictureArray.ToString();
                                        }
                                    }

                                    // 添加到动态属性字典
                                    dynamicAttributes[attributeName].Add(new AttributeOption
                                    {
                                        Id = attr.AttrId,
                                        Name = optionName,
                                        ColorCode = colorCode,
                                        ImagePath = imagePath,
                                        ExtraData = JsonConvert.SerializeObject(item.Value)
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing attribute options for {attributeName}: {ex.Message}");
                    }
                }

                // 查询收藏状态
                bool isFavorited = false;
                if (userId > 0)
                {
                    var favoriteRecord = await Db.Queryable<user_favorite>()
                        .Where(f => f.UserId == userId && f.ProId == productId)
                        .FirstAsync();
                    isFavorited = favoriteRecord != null;
                }

                // 构建响应模型
                var response = new ProductQuickViewResponse
                {
                    ProductId = product.ProId,
                    ProductName = product.Name_en,
                    PicPath = productImage?.PicPath ?? product.PicPath_0,
                    Price = product.Price_1 ?? 0,
                    PriceFormat = priceFormat,
                    OriginalPrice = product.Price_0 > product.Price_1 ? product.Price_0 : null,
                    OriginalPriceFormat = originalPriceFormat,
                    PromotionPriceFormat = promotionPriceFormat,
                    Rating = product.Rating,
                    ProductImages = imagesList,
                    ReviewCount = 0, // 默认值
                    BrandName = "Medica", // 默认品牌名称，因为Product类中没有Brand属性
                    CategoryName = categoryName,
                    IsInStock = product.SoldOut.HasValue,
                    SKU = product.SKU,
                    BriefDescription = product.BriefDescription_en,
                    IsCombination = product.IsCombination,
                    ProductVariants = combinations,
                    OptionalWarehouses = listOv,
                    DynamicAttributes = dynamicAttributes,
                    IsFavorited = isFavorited
                };

                return response;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品快速预览数据时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return null;
            }
        }


        /// <summary>
        /// 根据产品ID列表获取前端产品信息，支持分页
        /// </summary>
        /// <param name="productIds">产品ID列表</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页记录数</param>
        /// <param name="orderByField">排序字段，默认为ProId</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <param name="userId">当前用户ID（用于查询收藏状态，0表示未登录）</param>
        /// <returns>分页后的前端产品列表</returns>
        public async Task<PageModel<ProductFrontendResponse>> GetProductIdsByIdsList(List<int> productIds,
            int pageIndex = 1,
            int pageSize = 20, string orderByField = "ProId", string currentCurrency = "", int userId = 0)
        {
            try
            {
                // 检查参数是否为空
                if (productIds == null || !productIds.Any())
                {
                    return new PageModel<ProductFrontendResponse>
                    {
                        page = pageIndex,
                        PageSize = pageSize,
                        dataCount = 0,
                        data = new List<ProductFrontendResponse>()
                    };
                }

                // 验证分页参数
                if (pageIndex < 1) pageIndex = 1;
                if (pageSize < 1) pageSize = 20;

                // 创建产品查询对象
                var query = Db.Queryable<products>()
                    .Where(p => productIds.Contains(p.ProId));

                // 动态排序
                switch (orderByField?.ToLower())
                {
                    case "name":
                    case "name_en":
                        query = query.OrderBy(p => p.Name_en);
                        break;
                    case "price":
                    case "price_1":
                        query = query.OrderBy(p => p.Price_1);
                        break;
                    case "acctime":
                        query = query.OrderBy(p => p.AccTime);
                        break;
                    case "modifytime":
                        query = query.OrderBy(p => p.ModifyTime);
                        break;
                    case "proid desc":
                        query = query.OrderBy(p => p.ProId, OrderByType.Desc);
                        break;
                    case "name desc":
                    case "name_en desc":
                        query = query.OrderBy(p => p.Name_en, OrderByType.Desc);
                        break;
                    case "price desc":
                    case "price_1 desc":
                        query = query.OrderBy(p => p.Price_1, OrderByType.Desc);
                        break;
                    case "acctime desc":
                        query = query.OrderBy(p => p.AccTime, OrderByType.Desc);
                        break;
                    case "modifytime desc":
                        query = query.OrderBy(p => p.ModifyTime, OrderByType.Desc);
                        break;
                    default:
                        query = query.OrderBy(p => p.ProId);
                        break;
                }

                // 获取总记录数
                var totalCount = await query.CountAsync();

                // 执行分页查询，映射到ProductFrontendResponse
                var productList = await query
                    .Select(p => new ProductFrontendResponse
                    {
                        ProductId = p.ProId,
                        PageUrl = p.PageUrl,
                        ProductName = p.Name_en,
                        PicPath = p.PicPath_0,
                        PicPath_1 = p.PicPath_1,
                        Price = p.Price_1,
                        OriginalPrice = p.Price_0,
                        Rating = p.Rating,
                        BriefDescription = p.BriefDescription_en,
                        IsInStock = !p.SoldOut,
                        Stock = SqlFunc.Subqueryable<products_selected_attribute_combination>()
                            .Where(c => c.ProId == p.ProId)
                            .Sum(c => c.Stock) ?? 0,
                        AccTime = p.AccTime
                    })
                    .ToPageListAsync(pageIndex, pageSize);

                // 如果用户已登录，查询收藏状态
                if (userId > 0 && productList.Any())
                {
                    var productIdsForFavorite = productList.Select(p => p.ProductId).ToList();
                    var favoriteProductIds = await Db.Queryable<user_favorite>()
                        .Where(f => f.UserId == userId && productIdsForFavorite.Contains(f.ProId ?? 0))
                        .Select(f => f.ProId)
                        .ToListAsync();

                    // 更新产品的收藏状态
                    foreach (var product in productList)
                    {
                        product.IsFavorited = favoriteProductIds.Contains(product.ProductId);
                    }
                }

                // 获取币种信息用于价格格式化
                var userCurrency = await _currencyService.GetCurrency(currentCurrency);
                var manageCurrency = await _currencyService.GetManageDefaultCurrency();

                // 处理每个产品的前端特有逻辑
                foreach (var product in productList)
                {
                    // 获取促销价格信息
                    decimal promotionPrice = 0;
                    try
                    {
                        // 获取产品完整信息用于促销计算
                        var fullProduct = await Db.Queryable<products>().FirstAsync(p => p.ProId == product.ProductId);
                        if (fullProduct != null)
                        {
                            var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                                fullProduct,
                                product.Price ?? 0,
                                product.OriginalPrice ?? 0,
                                "", // variantsId
                                0 // userId
                            );

                            if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                            {
                                promotionPrice = flashSaleResult.Price;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"获取产品{product.ProductId}促销价格失败: {ex.Message}");
                        // 出错时促销价格保持为0
                        // promotionPrice = 0;
                    }

                    // 格式化价格
                    if (product.Price.HasValue)
                    {
                        try
                        {
                            var priceResult =
                                _currencyService.ShowPriceFormat(product.Price.Value, userCurrency, manageCurrency);
                            product.PriceFormat = priceResult.Item2;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "格式化产品价格时出错，ProductId: {ProductId}, Price: {Price}",
                                product.ProductId, product.Price.Value);
                            // 使用默认格式
                            product.PriceFormat = $"{userCurrency.Symbol}{product.Price.Value:F2}";
                        }
                    }

                    // 格式化原价
                    if (product.OriginalPrice.HasValue && product.OriginalPrice > product.Price)
                    {
                        try
                        {
                            var originalPriceResult = _currencyService.ShowPriceFormat(product.OriginalPrice.Value,
                                userCurrency, manageCurrency);
                            product.OriginalPriceFormat = originalPriceResult.Item2;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "格式化产品原价时出错，ProductId: {ProductId}, OriginalPrice: {OriginalPrice}",
                                product.ProductId, product.OriginalPrice.Value);
                            // 使用默认格式
                            product.OriginalPriceFormat = $"{userCurrency.Symbol}{product.OriginalPrice.Value:F2}";
                        }
                    }

                    // 正确的价格显示逻辑：原价始终不变，促销价作用在售价上
                    if (promotionPrice > 0)
                    {
                        try
                        {
                            var promotionPriceResult =
                                _currencyService.ShowPriceFormat(promotionPrice, userCurrency, manageCurrency);

                            // 有促销价时：现价位置显示促销价，原价保持不变
                            product.PriceFormat = promotionPriceResult.Item2; // 现价位置显示促销价
                            product.PromotionPrice = promotionPrice;
                            product.PromotionPriceFormat = promotionPriceResult.Item2;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex,
                                "格式化产品促销价格时出错，ProductId: {ProductId}, PromotionPrice: {PromotionPrice}",
                                product.ProductId, promotionPrice);
                            // 使用默认格式
                            product.PriceFormat = $"{userCurrency.Symbol}{promotionPrice:F2}";
                            product.PromotionPrice = promotionPrice;
                            product.PromotionPriceFormat = $"{userCurrency.Symbol}{promotionPrice:F2}";
                        }

                        // 原价位置显示真正的原价（OriginalPrice）
                        if (product.OriginalPrice.HasValue && product.OriginalPrice.Value > 0)
                        {
                            try
                            {
                                var originalPriceResult =
                                    _currencyService.ShowPriceFormat(product.OriginalPrice.Value, userCurrency,
                                        manageCurrency);
                                product.OriginalPriceFormat = originalPriceResult.Item2;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex,
                                    "格式化产品原价时出错，ProductId: {ProductId}, OriginalPrice: {OriginalPrice}",
                                    product.ProductId, product.OriginalPrice.Value);
                                // 使用默认格式
                                product.OriginalPriceFormat = $"{userCurrency.Symbol}{product.OriginalPrice.Value:F2}";
                            }
                        }
                    }
                    else
                    {
                        // 没有促销价时：正常显示现价和原价
                        // 格式化销售价格
                        if (product.Price.HasValue)
                        {
                            try
                            {
                                var priceResult =
                                    _currencyService.ShowPriceFormat(product.Price.Value, userCurrency, manageCurrency);
                                product.PriceFormat = priceResult.Item2;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "格式化产品价格时出错，ProductId: {ProductId}, Price: {Price}",
                                    product.ProductId, product.Price.Value);
                                // 使用默认格式
                                product.PriceFormat = $"{userCurrency.Symbol}{product.Price.Value:F2}";
                            }
                        }

                        // 如果原价大于售价，显示原价作为划线价
                        if (product.OriginalPrice.HasValue && product.OriginalPrice.Value > 0 &&
                            product.Price.HasValue && product.OriginalPrice.Value > product.Price.Value)
                        {
                            try
                            {
                                var originalPriceResult =
                                    _currencyService.ShowPriceFormat(product.OriginalPrice.Value, userCurrency,
                                        manageCurrency);
                                product.OriginalPriceFormat = originalPriceResult.Item2;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex,
                                    "格式化产品原价时出错，ProductId: {ProductId}, OriginalPrice: {OriginalPrice}",
                                    product.ProductId, product.OriginalPrice.Value);
                                // 使用默认格式
                                product.OriginalPriceFormat = $"{userCurrency.Symbol}{product.OriginalPrice.Value:F2}";
                            }
                        }
                    }

                    // 获取评论统计信息
                    // try
                    // {
                    //     var reviewStats = await GetProductReviewStats(product.ProductId);
                    //     product.AvgRating = reviewStats.avgRating;
                    //     product.ReviewCount = reviewStats.reviewCount;
                    // }
                    // catch (Exception ex)
                    // {
                    //     _logger.LogError($"获取产品{product.ProductId}评论统计失败: {ex.Message}");
                    //     product.AvgRating = 0;
                    //     product.ReviewCount = 0;
                    // }

                    // 设置产品类型标签
                    if (!product.IsInStock.HasValue || !product.IsInStock.Value)
                    {
                        product.TypeLabel = "Out Of Stock";
                    }
                    else if (product.IsNew)
                    {
                        product.TypeLabel = "New";
                    }
                    else if (product.IsHot)
                    {
                        product.TypeLabel = "Hot";
                    }
                }

                // 返回分页结果
                return new PageModel<ProductFrontendResponse>
                {
                    page = pageIndex,
                    PageSize = pageSize,
                    dataCount = (int)totalCount,
                    data = productList
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据产品ID列表查询前端产品时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                // 返回空的分页结果
                return new PageModel<ProductFrontendResponse>
                {
                    page = pageIndex,
                    PageSize = pageSize,
                    dataCount = 0,
                    data = new List<ProductFrontendResponse>()
                };
            }
        }

        /// <summary>
        /// 获取产品详情，用于前端Single页面展示
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <param name="userId">当前用户ID（用于查询收藏状态，0表示未登录）</param>
        /// <returns>产品详情数据</returns>
        public async Task<ProductDetailResponse> GetProductDetail(int productId, string currentCurrency = "", int userId = 0)
        {
            if (productId <= 0)
            {
                return null;
            }

            try
            {
                // 获取产品基本信息
                var product = await Db.Queryable<products>().FirstAsync(p => p.ProId == productId);
                if (product == null)
                {
                    return null;
                }

                // 获取产品描述信息
                var productDescription = await Db.Queryable<products_description>()
                    .FirstAsync(pd => pd.ProId == productId);

                // 获取产品SEO信息
                var productSeo = await Db.Queryable<products_seo>()
                    .FirstAsync(ps => ps.ProId == productId);

                // 获取产品图片
                var productImages = await Db.Queryable<products_images>()
                    .Where(pi => pi.ProId == productId)
                    .ToListAsync();

                // 获取产品分类
                var categoryRelates = await Db.Queryable<products_category_relate>()
                    .Where(pcr => pcr.ProId == productId)
                    .ToListAsync();

                string categoryName = "Uncategorized";
                if (categoryRelates != null && categoryRelates.Count > 0)
                {
                    var categoryIds = categoryRelates.Select(cr => cr.CateId).ToList();
                    var categories = await Db.Queryable<products_category>()
                        .Where(pc => categoryIds.Contains(pc.CateId))
                        .ToListAsync();

                    if (categories != null && categories.Count > 0)
                    {
                        categoryName = string.Join(", ", categories.Select(c => c.Category_en));
                    }
                }

                // 获取产品标签
                var tags = new List<TagItem>();
                if (!string.IsNullOrEmpty(product.Tags))
                {
                    // 从Tags字段中解析标签ID，格式如"|1|2|119|118|117|"
                    var tagIds = product.Tags.Split('|', StringSplitOptions.RemoveEmptyEntries)
                        .Select(int.Parse)
                        .ToList();

                    if (tagIds.Any())
                    {
                        // 根据标签ID查询标签信息
                        var productTags = await Db.Queryable<products_tags>()
                            .Where(pt => tagIds.Contains(pt.TId))
                            .ToListAsync();

                        tags = productTags.Select(pt => new TagItem
                        {
                            Id = pt.TId,
                            Name = pt.Name_en
                        }).ToList();
                    }
                }

                // 获取产品属性
                var productAttributes = await Db.Queryable<products_attribute>()
                    .Where(pa => pa.ProId == productId)
                    .ToListAsync();

                // 查询评论配置
                var jsonReview = await Db.Queryable<config>()
                    .Where(c => c.GroupId == "products_show" && c.Variable == "review")
                    .Select(c => c.Value)
                    .FirstAsync();

                // 解析评论配置
                bool needAudit = true; // 默认需要审核
                bool onlyOrderReviews = false; // 默认所有客户评论可见

                if (!string.IsNullOrEmpty(jsonReview))
                {
                    try
                    {
                        var reviewConfig = JsonConvert.DeserializeObject<ConfigReview>(jsonReview);
                        if (reviewConfig != null)
                        {
                            // range为1表示只有完成了订单评论可见
                            onlyOrderReviews = reviewConfig.range == 1;

                            // display为1表示评论需要审核即可见
                            needAudit = reviewConfig.display == 1;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析评论配置失败: {ex.Message}");
                    }
                }

                // 根据配置构建查询条件
                var reviewQuery = Db.Queryable<products_review>()
                    .Where(pr => pr.ProId == productId);

                // 如果需要审核，则只查询已审核的评论
                if (needAudit)
                {
                    reviewQuery = reviewQuery.Where(pr => pr.Audit == true);
                }

                // 如果只显示订单评论，则添加订单评论条件（这里假设OrderId不为空表示订单评论）
                // if (onlyOrderReviews)
                // {
                //     reviewQuery = reviewQuery.Where(pr => pr.OrderId != null && pr.OrderId != 0);
                // }

                // 查询产品评论
                var reviews = await reviewQuery
                    .OrderByDescending(pr => pr.AccTime)
                    .ToListAsync();

                // 计算平均评分
                decimal avgRating = 0;
                if (reviews != null && reviews.Count > 0)
                {
                    // 计算所有有效评分的平均值
                    var validRatings = reviews.Where(r => r.Rating.HasValue && r.Rating.Value > 0)
                        .Select(r => r.Rating.Value).ToList();
                    if (validRatings.Any())
                    {
                        avgRating = Math.Round((decimal)validRatings.Average(), 1); // 显式转换为decimal类型
                    }
                }

                // 获取币种信息
                var userCurrency = await _currencyService.GetCurrency(currentCurrency);
                var manageCurrency = await _currencyService.GetManageDefaultCurrency();

                // 处理价格信息
                decimal promotionPrice = 0;
                string priceFormat = null;
                string originalPriceFormat = null;
                string promotionPriceFormat = null;

                // 1. 获取促销价格信息
                try
                {
                    var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                        product,
                        product.Price_1.HasValue ? product.Price_1.Value : 0,
                        product.Price_0.HasValue ? product.Price_0.Value : 0,
                        "", // variantsId
                        0 // userId
                    );

                    if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                    {
                        promotionPrice = flashSaleResult.Price;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取产品{productId}促销价格失败: {ex.Message}");
                    // 出错时促销价格保持为0
                    // promotionPrice = 0;
                }

                // 2. 格式化促销价格（只有当促销价格大于0时才显示）
                if (promotionPrice > 0)
                {
                    var promotionPriceResult =
                        _currencyService.ShowPriceFormat(promotionPrice, userCurrency, manageCurrency);
                    promotionPriceFormat = promotionPriceResult.Item2;
                }

                // 3. 正确的价格显示逻辑：原价始终不变，促销价作用在售价上
                if (promotionPrice > 0)
                {
                    // 有促销价时：现价位置显示促销价，原价保持不变
                    priceFormat = promotionPriceFormat; // 现价位置显示促销价

                    // 原价位置显示真正的原价（Price_0）
                    if (product.Price_0.HasValue && product.Price_0.Value > 0)
                    {
                        var originalPriceResult =
                            _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                        originalPriceFormat = originalPriceResult.Item2;
                    }
                }
                else
                {
                    // 没有促销价时：正常显示现价和原价
                    // 格式化销售价格
                    if (product.Price_1.HasValue)
                    {
                        var priceResult =
                            _currencyService.ShowPriceFormat(product.Price_1.Value, userCurrency, manageCurrency);
                        priceFormat = priceResult.Item2;
                    }

                    // 如果原价大于售价，显示原价作为划线价
                    if (product.Price_0.HasValue && product.Price_0.Value > 0 &&
                        product.Price_1.HasValue && product.Price_0.Value > product.Price_1.Value)
                    {
                        var originalPriceResult =
                            _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                        originalPriceFormat = originalPriceResult.Item2;
                    }
                }

                // 计算库存状态
                var totalStock = await Db.Queryable<products_selected_attribute_combination>()
                    .Where(c => c.ProId == productId)
                    .SumAsync(c => c.Stock);

                // 获取产品变体
                var combinations = await Db.Queryable<products_selected_attribute_combination>()
                    .Where(c => c.ProId == productId).Select(t => new products_selected_attribute_combination
                    {
                        CId = t.CId,
                        ProId = t.ProId,
                        VariantsId = t.VariantsId,
                        ImageId = t.ImageId,
                        OvId = t.OvId,
                        PriorityShippingOvId = t.PriorityShippingOvId,
                        Title = t.Title,
                        SKU = t.SKU,
                        OldPrice = t.OldPrice,
                        PromotionPriceFormat = "0",
                        Price = t.Price,
                        CostPrice = t.CostPrice,
                        Stock = t.Stock,
                        Weight = t.Weight
                    })
                    .ToListAsync();

                // 单独处理图片路径
                if (combinations.Any())
                {
                    // 获取所有有效的ImageId
                    var imageIds = combinations
                        .Where(c => !string.IsNullOrEmpty(c.ImageId) && int.TryParse(c.ImageId, out _))
                        .Select(c => int.Parse(c.ImageId))
                        .Distinct()
                        .ToList();

                    // 批量查询图片信息
                    Dictionary<int, string> imagePaths = new Dictionary<int, string>();
                    if (imageIds.Any())
                    {
                        var images = await Db.Queryable<products_images>()
                            .Where(img => imageIds.Contains(img.ImageId) && img.ProId == productId)
                            .ToListAsync();

                        imagePaths = images.ToDictionary(img => img.ImageId, img => img.PicPath);
                    }

                    // 为每个变体设置图片路径
                    foreach (var combination in combinations)
                    {
                        if (!string.IsNullOrEmpty(combination.ImageId) &&
                            int.TryParse(combination.ImageId, out int imageId) &&
                            imagePaths.TryGetValue(imageId, out string picPath))
                        {
                            combination.PicPath = picPath;
                        }
                    }
                }

                // 处理每个变体的价格信息
                foreach (var variant in combinations)
                {
                    // 为每个变体计算促销价格
                    decimal variantPromotionPrice = 0;
                    try
                    {
                        var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                            product,
                            variant.Price ?? 0,
                            variant.OldPrice,
                            variant.VariantsId, // 使用变体ID
                            0 // userId
                        );

                        if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                        {
                            variantPromotionPrice = flashSaleResult.Price;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"获取产品{productId}变体{variant.VariantsId}促销价格失败: {ex.Message}");
                        // 出错时促销价格保持为0
                        // variantPromotionPrice = 0;
                    }

                    // 设置促销价格数值
                    variant.PromotionPrice = variantPromotionPrice;

                    // 正确的变体价格显示逻辑：原价始终不变，促销价作用在售价上
                    if (variantPromotionPrice > 0)
                    {
                        // 有促销价时：现价位置显示促销价，原价保持不变
                        var promotionPriceResult =
                            _currencyService.ShowPriceFormat(variantPromotionPrice, userCurrency, manageCurrency);
                        variant.PriceFormat = promotionPriceResult.Item2; // 现价位置显示促销价
                        variant.PromotionPriceFormat = promotionPriceResult.Item2;

                        // 原价位置显示真正的原价（OldPrice）
                        if (variant.OldPrice > 0)
                        {
                            var originalPriceResult =
                                _currencyService.ShowPriceFormat(variant.OldPrice, userCurrency, manageCurrency);
                            variant.OldPriceFormat = originalPriceResult.Item2;
                        }
                    }
                    else
                    {
                        // 没有促销价时：正常显示现价和原价
                        // 格式化销售价格并赋值给PriceFormat字段
                        if (variant.Price.HasValue)
                        {
                            var priceResult =
                                _currencyService.ShowPriceFormat(variant.Price.Value, userCurrency, manageCurrency);
                            variant.PriceFormat = priceResult.Item2;
                        }

                        // 格式化原价并赋值给OldPriceFormat字段（只有当原价大于销售价时才显示）
                        if (variant.OldPrice > 0 && variant.OldPrice > variant.Price)
                        {
                            var oldPriceResult =
                                _currencyService.ShowPriceFormat(variant.OldPrice, userCurrency, manageCurrency);
                            variant.OldPriceFormat = oldPriceResult.Item2;
                        }

                        // 没有促销价时PromotionPriceFormat保持为"0"
                        variant.PromotionPriceFormat = "0";
                    }
                }

                // 根据产品组合模式决定仓库查询逻辑
                List<shipping_overseas> listOv;
                if (product.IsCombination == 2)
                {
                    // 当IsCombination=2时，根据变体的Title去仓库表中查找匹配名字的仓库
                    var variantTitles = combinations.Where(c => !string.IsNullOrEmpty(c.Title))
                        .Select(c => c.Title.Trim())
                        .Distinct()
                        .ToList();

                    if (variantTitles.Any())
                    {
                        listOv = await Db.Queryable<shipping_overseas>()
                            .Where(w => variantTitles.Contains(w.Name.Trim()))
                            .OrderBy(w => w.MyOrder)
                            .ToListAsync();
                    }
                    else
                    {
                        // 如果没有有效的Title，返回空列表
                        listOv = new List<shipping_overseas>();
                    }
                }
                else
                {
                    // 按照原来的逻辑（根据OvId查询）
                    listOv = await Db.Queryable<shipping_overseas>()
                        .Where(w => combinations.Select(c => c.OvId).Contains(w.OvId))
                        .OrderBy(w => w.MyOrder)
                        .ToListAsync();
                }

                // 确定主图路径：优先使用PicPath_0，如果为空则使用ProductImages集合中的第一张图片
                string mainPicPath = product.PicPath_0;
                if (string.IsNullOrEmpty(mainPicPath) && productImages.Any())
                {
                    mainPicPath = productImages.First().PicPath;
                }

                // 查询收藏状态
                bool isFavorited = false;
                if (userId > 0)
                {
                    try
                    {
                        var favoriteRecord = await Db.Queryable<user_favorite>()
                            .Where(f => f.UserId == userId && f.ProId == productId)
                            .FirstAsync();
                        isFavorited = favoriteRecord != null;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "查询产品收藏状态时出错，ProductId: {ProductId}, UserId: {UserId}", productId, userId);
                        // 出错时默认为未收藏
                        isFavorited = false;
                    }
                }

                // 构建响应对象
                var response = new ProductDetailResponse
                {
                    ProductVariants = combinations,
                    IsCombination = product.IsCombination,
                    OptionalWarehouses = listOv,
                    ProductId = product.ProId,
                    AvgRating = Math.Round(avgRating, 1),
                    ProductName = product.Name_en,
                    BriefDescription = product.BriefDescription_en,
                    Sku = product.SKU,
                    // Price = product.Price_1.HasValue ? product.Price_1.Value : 0, //销售价
                    PriceFormat = priceFormat, //格式化销售价
                    // OriginalPrice = product.Price_0 > product.Price_1 ? product.Price_0 : (decimal?)null, //原价
                    OriginalPriceFormat = originalPriceFormat, //格式化原价
                    // PromotionPrice = promotionPrice, //促销价
                    PromotionPriceFormat = promotionPriceFormat, //格式化促销价
                    PicPath = mainPicPath, // 使用确定的主图路径
                    Rating = product.Rating ?? 0,
                    ReviewCount = reviews.Count,
                    IsInStock = totalStock > 0 && product.SoldOut == false,
                    BrandName = "Medica", // 默认品牌名称
                    CategoryName = categoryName,
                    ProductImages = productImages,
                    Tags = tags,
                    Specifications = new List<SpecificationItem>(),
                    ProductSeo = productSeo,
                    IsFavorited = isFavorited // 添加收藏状态
                };

                // 设置描述
                if (productDescription != null)
                {
                    response.Description = productDescription.Description_en;
                }

                // 添加特性列表
                response.Features = new List<string>();
                // 动态处理所有属性
                foreach (var attr in productAttributes)
                {
                    // 使用属性的Name_en作为动态键名
                    string attributeName = attr.Name_en;

                    // 初始化该属性的选项列表
                    if (!response.DynamicAttributes.ContainsKey(attributeName))
                    {
                        response.DynamicAttributes[attributeName] = new List<AttributeOption>();
                    }

                    // 统一处理所有属性类型
                    try
                    {
                        // 优先处理OptionsData字段（通常用于颜色等复杂属性）
                        if (!string.IsNullOrEmpty(attr.OptionsData))
                        {
                            // 处理复杂属性数据（如颜色）
                            var optionsData = attr.OptionsData.Trim();
                            var colorDict =
                                JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, object>>>(
                                    optionsData);
                            if (colorDict != null)
                            {
                                foreach (var item in colorDict)
                                {
                                    string optionName = item.Key;
                                    string colorCode = null;
                                    string imagePath = null;

                                    // 获取颜色代码
                                    if (item.Value.ContainsKey("color") && item.Value["color"] != null)
                                    {
                                        colorCode = item.Value["color"].ToString();
                                    }

                                    // 获取图片路径（如果有）
                                    if (item.Value.ContainsKey("picture") && item.Value["picture"] != null)
                                    {
                                        var pictureArray = item.Value["picture"];
                                        if (pictureArray != null && pictureArray.ToString() != "[]")
                                        {
                                            imagePath = pictureArray.ToString();
                                        }
                                    }

                                    // 添加到动态属性字典
                                    response.DynamicAttributes[attributeName].Add(new AttributeOption
                                    {
                                        Id = attr.AttrId,
                                        Name = optionName,
                                        ColorCode = colorCode,
                                        ImagePath = imagePath,
                                        ExtraData = JsonConvert.SerializeObject(item.Value)
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing attribute options for {attributeName}: {ex.Message}");
                    }

                    // 同时添加到规格列表（用于显示）
                    response.Specifications.Add(new SpecificationItem
                    {
                        Name = attr.Name_en,
                        Value = attr.Options
                    });
                }

                // 获取产品评论
                try
                {
                    if (reviews.Any())
                    {
                        var reviewItems = new List<ReviewItem>();

                        foreach (var review in reviews)
                        {
                            var reviewItem = new ReviewItem
                            {
                                Id = review.RId,
                                UserName = !string.IsNullOrEmpty(review.CustomerName)
                                    ? review.CustomerName
                                    : "Anonymous",
                                ReviewDate = DateTimeOffset.FromUnixTimeSeconds(review.AccTime ?? 0).DateTime,
                                Content = review.Content ?? string.Empty,
                                Rating = review.Rating ?? 0,
                                UserAvatar = "/themes/t600/assets/img/icon-img/user.png", // 使用默认头像
                                Images = new List<string>(),
                                Replies = new List<ReviewReplyItem>()
                            };

                            // 添加评论图片
                            if (!string.IsNullOrEmpty(review.PicPath_0))
                                reviewItem.Images.Add(review.PicPath_0);
                            if (!string.IsNullOrEmpty(review.PicPath_1))
                                reviewItem.Images.Add(review.PicPath_1);
                            if (!string.IsNullOrEmpty(review.PicPath_2))
                                reviewItem.Images.Add(review.PicPath_2);

                            // 获取评论的所有回复
                            var replies = await Db.Queryable<products_review_reply>()
                                .Where(r => r.RId == review.RId) // 通过评论ID查询所有回复
                                .OrderBy(r => r.AccTime) // 按时间排序，最早的回复在前
                                .ToListAsync();

                            if (replies != null && replies.Count > 0)
                            {
                                foreach (var reply in replies)
                                {
                                    reviewItem.Replies.Add(new ReviewReplyItem
                                    {
                                        Id = reply.ReId,
                                        UserName = !string.IsNullOrEmpty(reply.ResponderName)
                                            ? reply.ResponderName
                                            : reply.Identity ?? "Admin",
                                        ReplyDate = DateTimeOffset.FromUnixTimeSeconds(reply.AccTime ?? 0).DateTime,
                                        Content = reply.Content ?? string.Empty,
                                        IsAdmin = reply.Identity?.ToLower() == "admin",
                                        ReplyToName = reply.ReplyToName,
                                        ReplyToId = reply.ReplyToId,
                                        Identity = reply.Identity ?? "admin"
                                    });
                                }
                            }

                            reviewItems.Add(reviewItem);
                        }

                        response.Reviews = reviewItems;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting product reviews: {ex.Message}");
                    // 出错时不影响整体返回，只是评论列表为空
                    response.Reviews = new List<ReviewItem>();
                }

                return response;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetProductDetail: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取相关产品
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <param name="count">获取数量</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <returns>相关产品列表</returns>
        public async Task<List<ProductFrontendResponse>> GetRelatedProducts(int productId, int count,
            string currentCurrency = "")
        {
            if (productId <= 0 || count <= 0)
            {
                return new List<ProductFrontendResponse>();
            }

            try
            {
                // 获取当前产品的分类ID列表
                var categoryIds = await Db.Queryable<products_category_relate>()
                    .Where(pcr => pcr.ProId == productId)
                    .Select(pcr => pcr.CateId)
                    .ToListAsync();

                if (categoryIds.Count == 0)
                {
                    return new List<ProductFrontendResponse>();
                }

                // 获取同分类的其他产品ID（排除当前产品）
                var relatedProductIds = await Db.Queryable<products_category_relate>()
                    .Where(pcr => categoryIds.Contains(pcr.CateId) && pcr.ProId != productId)
                    .Select(pcr => pcr.ProId)
                    .Distinct()
                    .ToListAsync();

                if (relatedProductIds.Count == 0)
                {
                    return new List<ProductFrontendResponse>();
                }

                // 获取相关产品信息
                var products = await Db.Queryable<products>()
                    .Where(p => relatedProductIds.Contains(p.ProId) && p.SoldOut == false)
                    .Take(count)
                    .ToListAsync();

                // 获取产品图片信息
                var productIds = products.Select(p => p.ProId).ToList();
                var productImages = await Db.Queryable<products_images>()
                    .Where(img => productIds.Contains(img.ProId) && img.Position == 1)
                    .ToListAsync();

                // 创建产品图片字典，方便快速查找
                var productImageDict = productImages.ToDictionary(img => img.ProId, img => img.PicPath);

                // 获取币种信息
                var userCurrency = await _currencyService.GetCurrency(currentCurrency);
                var manageCurrency = await _currencyService.GetManageDefaultCurrency();

                // 构建前端响应对象
                var result = new List<ProductFrontendResponse>();
                foreach (var product in products)
                {
                    string picPath = product.PicPath_0; // 默认使用产品表的主图
                    if (productImageDict.TryGetValue(product.ProId, out string imagePath) &&
                        !string.IsNullOrEmpty(imagePath))
                    {
                        picPath = imagePath; // 如果存在专门的图片记录，则使用该图片
                    }

                    // 获取促销价格信息
                    decimal promotionPrice = 0;
                    string promotionPriceFormat = null;
                    try
                    {
                        var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                            product,
                            product.Price_1 ?? 0,
                            product.Price_0 ?? 0,
                            "", // variantsId
                            0 // userId
                        );

                        if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                        {
                            promotionPrice = flashSaleResult.Price;
                            var promotionPriceResult =
                                _currencyService.ShowPriceFormat(promotionPrice, userCurrency, manageCurrency);
                            promotionPriceFormat = promotionPriceResult.Item2;
                        }
                    }
                    catch (Newtonsoft.Json.JsonSerializationException jsonEx)
                    {
                        Console.WriteLine($"获取相关产品{product.ProId}促销价格失败 - JSON格式错误: {jsonEx.Message}");
                        // JSON格式错误时促销价格保持为0，产品正常显示
                        promotionPrice = 0;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"获取相关产品{product.ProId}促销价格失败: {ex.Message}");
                        // 出错时促销价格保持为0
                        // promotionPrice = 0;
                    }

                    // 正确的价格显示逻辑：原价始终不变，促销价作用在售价上
                    string priceFormat = null;
                    string originalPriceFormat = null;

                    if (promotionPrice > 0)
                    {
                        // 有促销价时：现价位置显示促销价，原价保持不变
                        priceFormat = promotionPriceFormat; // 现价位置显示促销价

                        // 原价位置显示真正的原价（Price_0）
                        if (product.Price_0.HasValue && product.Price_0.Value > 0)
                        {
                            var originalPriceResult =
                                _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                            originalPriceFormat = originalPriceResult.Item2;
                        }
                    }
                    else
                    {
                        // 没有促销价时，如果原价大于售价，显示原价作为划线价
                        if (product.Price_0.HasValue && product.Price_0.Value > 0 &&
                            product.Price_1.HasValue && product.Price_0.Value > product.Price_1.Value)
                        {
                            var originalPriceResult =
                                _currencyService.ShowPriceFormat(product.Price_0.Value, userCurrency, manageCurrency);
                            originalPriceFormat = originalPriceResult.Item2;
                        }
                    }

                    var response = new ProductFrontendResponse
                    {
                        ProductId = product.ProId,
                        ProductName = product.Name_en,
                        PicPath = picPath,
                        Price = product.Price_1,
                        PriceFormat = priceFormat,
                        OriginalPrice = product.Price_0 > product.Price_1 ? product.Price_0 : null,
                        OriginalPriceFormat = originalPriceFormat,
                        PromotionPrice = promotionPrice > 0 ? promotionPrice : (decimal?)null,
                        PromotionPriceFormat = promotionPriceFormat,
                        Rating = product.Rating,
                        IsInStock = !product.SoldOut.HasValue && product.Stock > 0,
                        BriefDescription = product.BriefDescription_en
                    };

                    // 获取评论统计信息
                    // try
                    // {
                    //     var reviewStats = await GetProductReviewStats(response.ProductId);
                    //     response.AvgRating = reviewStats.avgRating;
                    //     response.ReviewCount = reviewStats.reviewCount;
                    // }
                    // catch (Exception ex)
                    // {
                    //     _logger.LogError($"获取产品{response.ProductId}评论统计失败: {ex.Message}");
                    //     response.AvgRating = 0;
                    //     response.ReviewCount = 0;
                    // }

                    // 设置产品类型标签
                    if (!response.IsInStock.HasValue)
                    {
                        response.TypeLabel = "Out Of Stock";
                    }
                    else if (response.IsNew)
                    {
                        response.TypeLabel = "New";
                    }
                    else if (response.IsHot)
                    {
                        response.TypeLabel = "Hot";
                    }


                    result.Add(response);
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取相关产品时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return new List<ProductFrontendResponse>();
            }
        }

        /// <summary>
        /// 获取产品的评论统计信息（平均评分和评论数量）
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <returns>返回平均评分和评论数量的元组</returns>
        private async Task<(decimal avgRating, int reviewCount)> GetProductReviewStats(int productId)
        {
            try
            {
                // 查询评论配置
                var jsonReview = await Db.Queryable<config>()
                    .Where(c => c.GroupId == "products_show" && c.Variable == "review")
                    .Select(c => c.Value)
                    .FirstAsync();

                // 解析评论配置
                bool needAudit = true; // 默认需要审核
                bool onlyOrderReviews = false; // 默认所有客户评论可见

                if (!string.IsNullOrEmpty(jsonReview))
                {
                    try
                    {
                        var reviewConfig = JsonConvert.DeserializeObject<ConfigReview>(jsonReview);
                        if (reviewConfig != null)
                        {
                            // range为1表示只有完成了订单评论可见
                            onlyOrderReviews = reviewConfig.range == 1;

                            // display为1表示评论需要审核即可见
                            needAudit = reviewConfig.display == 1;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"解析评论配置失败: {ex.Message}");
                    }
                }

                // 根据配置构建查询条件
                var reviewQuery = Db.Queryable<products_review>()
                    .Where(pr => pr.ProId == productId);

                // 如果需要审核，则只查询已审核的评论
                if (needAudit)
                {
                    reviewQuery = reviewQuery.Where(pr => pr.Audit == true);
                }

                // 查询产品评论
                var reviews = await reviewQuery
                    .OrderByDescending(pr => pr.AccTime)
                    .ToListAsync();

                // 计算平均评分和评论数量
                decimal avgRating = 0;
                int reviewCount = reviews?.Count ?? 0;

                if (reviews != null && reviews.Count > 0)
                {
                    // 计算所有有效评分的平均值
                    var validRatings = reviews.Where(r => r.Rating.HasValue && r.Rating.Value > 0)
                        .Select(r => r.Rating.Value).ToList();
                    if (validRatings.Any())
                    {
                        avgRating = Math.Round((decimal)validRatings.Average(), 1); // 显式转换为decimal类型
                    }
                }

                return (avgRating, reviewCount);
            }
            catch (Exception ex)
            {
                _logger.LogError($"获取产品评论统计失败: {ex.Message}");
                return (0, 0);
            }
        }
    }
}