using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.ProductVisual;
using YseStore.IService.Visual;

namespace YseStore.Service.ProductVisual
{
    /// <summary>
    ///
    /// </summary>
    public class ProductVisualTemplateServicee : BaseServices<product_visual_template>, IProductVisualTemplateServicee
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<ProductVisualTemplateServicee> _logger;

        public ProductVisualTemplateServicee(ISqlSugarClient db, ICaching caching, ILogger<ProductVisualTemplateServicee> logger)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
        }

       
    }
}
