using YseStore.Model;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.Response.Products;

namespace YseStore.IService.Products
{
    /// <summary>
    /// 产品前端服务接口
    /// </summary>
    public interface IProductFrontService
    {
        /// <summary>
        /// 获取前端展示的产品列表，支持搜索和分页
        /// </summary>
        /// <param name="queryRequest">前端查询请求参数</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <param name="userId">当前用户ID（用于查询收藏状态，0表示未登录）</param>
        /// <returns>分页后的前端产品列表</returns>
        Task<PageModel<ProductFrontendResponse>> GetFrontendProductList(ProductFrontendQueryRequest queryRequest, string currentCurrency = "", int userId = 0);

        /// <summary>
        /// 获取产品快速预览数据
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <param name="userId">用户ID，用于查询收藏状态</param>
        /// <returns>产品快速预览数据</returns>
        Task<ProductQuickViewResponse> GetProductQuickView(int productId, string currentCurrency = "", int userId = 0);

        /// <summary>
        /// 获取前端产品列表，支持搜索和分页
        /// </summary>
        /// <param name="queryRequest">查询请求参数</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <returns>分页后的前端产品列表</returns>
        // Task<PageModel<ProductFrontendResponse>> GetProductList(ProductQueryRequest queryRequest, string currentCurrency = "");

        /// <summary>
        /// 根据产品ID列表获取前端产品信息，支持分页
        /// </summary>
        /// <param name="productIds">产品ID列表</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页记录数</param>
        /// <param name="orderByField">排序字段，默认为ProId</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <param name="userId">当前用户ID（用于查询收藏状态，0表示未登录）</param>
        /// <returns>分页后的前端产品列表</returns>
        Task<PageModel<ProductFrontendResponse>> GetProductIdsByIdsList(List<int> productIds, int pageIndex = 1,
            int pageSize = 20, string orderByField = "ProId", string currentCurrency = "", int userId = 0);
        
        /// <summary>
        /// 获取产品详情，用于前端Single页面展示
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <returns>产品详情数据</returns>
        Task<ProductDetailResponse> GetProductDetail(int productId, string currentCurrency = "", int userId = 0);
        
        /// <summary>
        /// 获取相关产品
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <param name="count">获取数量</param>
        /// <param name="currentCurrency">当前用户币种</param>
        /// <returns>相关产品列表</returns>
        Task<List<ProductFrontendResponse>> GetRelatedProducts(int productId, int count, string currentCurrency = "");
    }
}
